"use client";

import { Button } from "@/components/ui/button";
import { useCartStore } from "@/app/[locale]/(cart)/cartStore";
import { useSizeStore } from "@/app/[locale]/(cart)/sizeStore";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import { cn } from "@/lib/utils";
import { CartDialog } from "@/app/[locale]/(cart)/CartDialog";

type Props = {
  className?: string;
};

export const BuyNowButton = ({ className }: Props) => {
  const t = useTranslations("ProductDetails");
  const tCart = useTranslations("Cart");
  const { addItem } = useCartStore();
  const { selectedSize } = useSizeStore();

  const handleBuyNow = () => {
    if (!selectedSize) {
      toast.error(t("selectSizeFirst"), {
        closeButton: true,
      });
      return;
    }

    const item = {
      id: t("productId"),
      name: t("productName"),
      price: 290_000,
      image: sneakerSideLeft.src,
      size: selectedSize,
      quantity: 1,
    };

    addItem(item, {
      messages: {
        alreadyExists: tCart("sizeAlreadyExists"),
        addedToCart: t("addedToCart"),
      },
    });
  };

  return (
    <CartDialog>
      <Button className={cn(className)} onClick={handleBuyNow}>
        {t("buyNow")}
      </Button>
    </CartDialog>
  );
};
