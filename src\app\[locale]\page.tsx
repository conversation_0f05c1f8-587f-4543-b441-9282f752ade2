import { setRequestLocale } from "next-intl/server";
import ShopsSection from "./ShopsSection";
import AboutSection from "./AboutSection";
import HeroSection from "./HeroSection";
import HeroSlider from "./HeroSlider";
import ProductDetailsSection from "./ProductDetailsSection";

import { routing } from "@/i18n/routing";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

type Props = {
  params: Promise<{ locale: (typeof routing.locales)[number] }>;
};

export default async function HomePage({ params }: Props) {
  const { locale } = await params;
  setRequestLocale(locale);

  return (
    <main className="flex min-h-screen flex-col">
      <HeroSlider className="w-full pt-5" />
      <HeroSection className="mx-auto w-full max-w-[90rem] px-6 py-20 sm:px-20" />
      <AboutSection className="mx-auto w-full max-w-[90rem] px-6 py-20 sm:px-20" />
      <div className="relative z-10 h-10 w-full overflow-visible">
        <div className="bg-secondary-foreground absolute bottom-[10%] left-1/2 -z-10 h-[50%] w-full -translate-x-1/2 [border-radius:50%] [box-shadow:0_10px_25px_15px_hsl(43,100%,16%)]" />
        <div className="bg-primary-foreground absolute h-full w-full" />
      </div>
      <ProductDetailsSection className="mx-auto w-full max-w-[90rem] px-6 py-20 sm:px-20" />
      <ShopsSection className="mx-auto w-full max-w-[90rem] px-6 py-20 sm:px-20" />
    </main>
  );
}
