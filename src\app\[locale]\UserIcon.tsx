"use client";
import { <PERSON> } from "@/i18n/navigation";
import { User2 } from "lucide-react";
import { linksObj } from "../links";
import { authClient } from "@/lib/auth/client";
import { useMemo } from "react";

const UserIcon = () => {
  const { data: session } = authClient.useSession();
  const user = useMemo(() => session?.user, [session]);

  const initial = useMemo(() => {
    const [firstName, lastName] = user?.name?.split(" ") ?? [];
    if (!firstName || !lastName) return "??";
    return `${firstName.slice(0, 10)}`;
  }, [user]);

  return (
    <Link
      href={user ? linksObj.profile.href : linksObj.signOn.href}
      className="relative hocus:text-accent"
    >
      {user && (
        <span className="absolute uppercase -bottom-2  right-1/2 translate-x-1/2 px-1 py-0 text-[0.5rem]  rounded-full bg-accent flex items-center justify-center font-semibold text-foreground">
          {initial}
        </span>
      )}
      <User2 className="size-4 sm:size-5" strokeWidth={1.5} />
    </Link>
  );
};

export default UserIcon;
