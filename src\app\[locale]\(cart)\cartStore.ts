import { create } from "zustand";
import { persist } from "zustand/middleware";
import { SizeInfo } from "@/components/SizeSelector";
import { ShopInfoType } from "@/app/[locale]/data";
import { toast } from "sonner";

export type CartItem = {
  id: string;
  name: string;
  price: number;
  image: string;
  size: SizeInfo;
  quantity: number;
};

// type UpdateSizeResult = {
//   success: boolean;
//   error?: "same-size" | "out-of-stock" | "size-exists";
// };

type UpdateSizeProps = {
  itemId: string;
  oldSize: SizeInfo;
  newSize: SizeInfo;
  messages: {
    outOfStock: string;
    sizeAlreadyExists: string;
    sizeUpdated: string;
  };
};

type CartStore = {
  items: CartItem[];
  selectedShop: ShopInfoType | null;
  setSelectedShop: (store: ShopInfoType | null) => void;
  setItems: (items: CartItem[]) => void;
  addItem: (
    item: CartItem,
    props: { messages: { alreadyExists: string; addedToCart: string } },
  ) => void;
  removeItem: (itemId: string, size: SizeInfo) => void;
  updateQuantity: (itemId: string, size: SizeInfo, quantity: number) => void;
  updateSize: (props: UpdateSizeProps) => void;
  clearCart: () => void;
};

export const useCartStore = create<CartStore>()(
  persist(
    (set) => ({
      items: [],
      selectedShop: null,
      setSelectedShop: (store) => set({ selectedShop: store }),
      setItems: (items) => set({ items }),
      addItem: (item, { messages }) =>
        set((state) => {
          const existingItemIndex = state.items.findIndex(
            (i) => i.id === item.id && i.size.euSize === item.size.euSize,
          );

          if (existingItemIndex > -1) {
            toast.error(messages.alreadyExists, {
              closeButton: true,
            });
            return state;
          }

          toast.success(messages.addedToCart);
          return { items: [...state.items, item] };
        }),
      removeItem: (itemId, size) =>
        set((state) => ({
          items: state.items.filter(
            (item) => !(item.id === itemId && item.size.euSize === size.euSize),
          ),
        })),
      updateQuantity: (itemId, size, quantity) =>
        set((state) => ({
          items: state.items.map((item) =>
            item.id === itemId && item.size.euSize === size.euSize
              ? { ...item, quantity }
              : item,
          ),
        })),
      updateSize: ({ itemId, oldSize, newSize, messages }) => {
        if (newSize.euSize === oldSize.euSize) return;

        if (newSize.stock <= 0) {
          toast.error(messages.outOfStock, {
            closeButton: true,
          });
          return;
        }

        set((state) => {
          const existingItem = state.items.find(
            (item) => item.id === itemId && item.size.euSize === newSize.euSize,
          );

          if (existingItem) {
            toast.error(messages.sizeAlreadyExists, {
              closeButton: true,
            });
            return state;
          }

          toast.success(messages.sizeUpdated);
          return {
            items: state.items.map((item) =>
              item.id === itemId && item.size.euSize === oldSize.euSize
                ? { ...item, size: newSize, quantity: 1 }
                : item,
            ),
          };
        });
      },
      clearCart: () => set({ items: [], selectedShop: null }),
    }),
    {
      name: "cart-storage",
    },
  ),
);
