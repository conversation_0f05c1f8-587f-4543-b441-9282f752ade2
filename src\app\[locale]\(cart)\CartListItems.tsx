"use client";
import { useTranslations } from "next-intl";
import { Fragment } from "react";
import { X, Plus, Minus } from "lucide-react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import Text from "@/components/Text";
import { cn } from "@/lib/utils";
import { formatKwanza } from "@/lib/currency";
import { CartItem, useCartStore } from "./cartStore";
import { SizeInfo, SizeSelector } from "@/components/SizeSelector";
import { availableSizes } from "@/app/[locale]/data";
import { useAutoAnimate } from "@formkit/auto-animate/react";

type CartListItemsProps = {
  items: CartItem[];
  removeItem: (id: string, size: SizeInfo) => void;
  className?: string;
};

const CartListItems = ({
  items,
  removeItem,
  className,
}: CartListItemsProps) => {
  const t = useTranslations("Cart");
  const updateQuantity = useCartStore((state) => state.updateQuantity);
  const updateSize = useCartStore((state) => state.updateSize);
  const [parent] = useAutoAnimate();

  const handleDecreaseQuantity = (item: CartItem) => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.size, item.quantity - 1);
    }
  };

  const handleIncreaseQuantity = (item: CartItem) => {
    if (item.quantity < item.size.stock) {
      updateQuantity(item.id, item.size, item.quantity + 1);
    }
  };

  if (items.length === 0) {
    return (
      <div className="text-muted-foreground py-10 text-center">
        {t("empty")}
      </div>
    );
  }

  return (
    <ScrollArea className="max-h-[80vh] w-full p-4">
      <ul
        ref={parent}
        role="list"
        className={cn("flex max-h-[40vh] flex-col gap-4", className)}
      >
        {items.map((item, i) => (
          <Fragment key={`${item.id}-${item.size.euSize}`}>
            <li className="not-last:border-muted-foreground flex items-start gap-4">
              <article className="xs:grid-cols-4 grid flex-1 grid-cols-3 gap-4 md:gap-8">
                <picture className="bg-muted xs max-xs:col-span-full aspect-square w-full min-w-24 flex-shrink-0 overflow-hidden rounded-md p-1 max-md:row-span-3">
                  <Image
                    src={item.image}
                    alt={item.name}
                    width={800}
                    height={800}
                    className="h-full w-full object-contain object-center"
                  />
                </picture>
                <div className="max-xs:col-span-2 flex flex-col gap-1">
                  <Text as="p" size="xs">
                    {t("newSeason")}
                  </Text>
                  <Text as="h6" className="font-semibold">
                    {item.name}
                  </Text>
                  <Text as="p" size="xs">
                    {item.id}
                  </Text>
                  <Text
                    as="p"
                    size="xs"
                    className="bg-muted/50 text-muted-foreground w-fit px-2 py-1"
                  >
                    {t("lastLeft", { count: item.size.stock })}
                  </Text>
                </div>
                <Text as="p" size="sm">
                  {formatKwanza(item.price)}
                </Text>
                <div className="max-xs:col-span-2 flex flex-col gap-4">
                  <span className="@container flex flex-col">
                    <Text as="p" size="xs">
                      {t("sizeLabel")}
                    </Text>

                    <Text
                      as="p"
                      size="sm"
                      className="flex items-center gap-x-2 @max-[6rem]:flex-col @max-[6rem]:items-start"
                    >
                      <b className="font-semibold">{item.size.ukSize} Uk</b>
                      <SizeSelector
                        sizes={availableSizes}
                        onSelectedSize={(newSize) =>
                          updateSize({
                            itemId: item.id,
                            oldSize: item.size,
                            newSize,
                            messages: {
                              outOfStock: t("outOfStock"),
                              sizeAlreadyExists: t("sizeAlreadyExists"),
                              sizeUpdated: t("sizeUpdated"),
                            },
                          })
                        }
                      >
                        <Button
                          size="sm"
                          variant="link"
                          className="!p-0 lowercase"
                        >
                          {t("changeSizeShort")}
                        </Button>
                      </SizeSelector>
                    </Text>
                  </span>
                  <span className="flex w-fit flex-col gap-1">
                    <Text as="p" size="xs">
                      {t("quantityLabel")}
                    </Text>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        className="size-6 rounded-full p-0"
                        onClick={() => handleDecreaseQuantity(item)}
                        disabled={item.quantity <= 1}
                        aria-label={t("remove")}
                      >
                        <Minus className="size-3" />
                      </Button>
                      <Text as="p" size="xs" className="font-semibold">
                        {item.quantity}
                      </Text>
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        className="size-6 rounded-full p-0"
                        onClick={() => handleIncreaseQuantity(item)}
                        disabled={item.quantity >= item.size.stock}
                        aria-label={t("quantity")}
                      >
                        <Plus className="size-3" />
                      </Button>
                    </div>
                  </span>
                </div>
              </article>
              <button
                type="button"
                onClick={() => removeItem(item.id, item.size)}
                className="font-medium text-gray-500 hover:text-gray-700"
                aria-label={t("removeItem")}
              >
                <X className="size-5" />
              </button>
            </li>
            {i !== items.length - 1 && <Separator />}
          </Fragment>
        ))}
      </ul>
    </ScrollArea>
  );
};

export default CartListItems;
