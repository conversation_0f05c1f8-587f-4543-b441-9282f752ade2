import { NextIntlClientProvider, hasLocale } from "next-intl";
import { getTranslations, setRequestLocale } from "next-intl/server";
import { notFound } from "next/navigation";
import localFont from "next/font/local";
import { LOCALES, routing } from "@/i18n/routing";
import "../globals.css";
import Header from "./Header";
import Footer from "@/components/Footer";
import { Toaster } from "@/components/ui/sonner";
import type { ReactNode } from "react";

const mainFont = localFont({
  src: [
    {
      path: "../font/Politica-Light.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../font/Politica.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../font/Politica-Bold.otf",
      weight: "700",
      style: "normal",
    },
    {
      path: "../font/Politica-ExtraBold.otf",
      weight: "800",
      style: "normal",
    },
  ],
  variable: "--font-main",
  display: "swap",
});

const secondaryFont = localFont({
  src: [
    {
      path: "../font/MyriadPro-Light.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../font/MyriadPro-Regular.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../font/MyriadPro-Bold.otf",
      weight: "700",
      style: "normal",
    },
  ],
  variable: "--font-secondary",
  display: "swap",
});

type Props = {
  children: ReactNode;
  params: Promise<{ locale: (typeof LOCALES)[number] }>;
};

export async function generateMetadata({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "Metadata" });

  return {
    title: t("title"),
    description: t("description"),
    icons: {
      icon: "/favicon.png",
    },
    metadataBase: new URL("https://kurguen.wacriativos.com"),
    alternates: {
      canonical: "/",
    },
    openGraph: {
      images: [
        {
          url: "https://kurguen.wacriativos.com/hero.jpg",
          width: 1200,
          height: 630,
          alt: t("title"),
        },
      ],
      locale,
      type: "website",
      siteName: "Kurguen",
    },
  };
}

export default async function LocaleLayout({ children, params }: Props) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <html lang={locale} className="h-full">
      <body
        className={`${mainFont.variable} ${secondaryFont.variable} antialiased flex flex-col min-h-full`}
      >
        <NextIntlClientProvider>
          <Header className="mx-auto max-w-[90rem] px-6 py-5 sm:px-20" />
          {children}
          <Footer />
          <Toaster />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
