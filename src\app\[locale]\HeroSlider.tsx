import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPagination,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";
import { getSliders } from "./slides-data";
import HeroSliderItem from "./HeroSliderItem";

type Props = { className?: string };

const HeroSlider = async ({ className }: Props) => {
  const sliders = await getSliders();
  return (
    <Carousel className={cn("w-full text-white", className)}>
      <CarouselContent>
        {sliders.map((item, index) => (
          <CarouselItem key={index} className="flex items-center">
            <HeroSliderItem
              item={item}
              className="mx-auto w-full max-w-[90rem] px-6 sm:px-20"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPagination className="mt-8" />
    </Carousel>
  );
};

export default HeroSlider;
