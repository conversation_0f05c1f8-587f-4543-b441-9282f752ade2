"use server";

import { CartItem } from "@/app/[locale]/(cart)/cartStore";
import { auth } from "@/lib/auth/server";
import {
  orderConfirmationEmailTemplate,
  orderNotificationEmailTemplate,
  sendEmail,
} from "@/lib/mailer";
import prisma from "@/lib/prisma";
import { getTranslations } from "next-intl/server";
import { headers } from "next/headers";
import { z } from "zod";
import { SubmitOrderData, submitOrderSchema } from "./model";
import ShortUniqueId from 'short-unique-id';

export const getUserCart = async () => {
  try {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user.id) return null;

    const cart = await prisma.cart.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    return cart;
  } catch (error) {
    console.error("Error fetching user cart:", error);
    return null;
  }
};

export const saveCart = async (cartItems: CartItem[]) => {
  try {
    const session = await auth.api.getSession({ headers: await headers() });
    if (!session?.user.id) return null;

    const cart = await prisma.cart.upsert({
      where: {
        userId: session.user.id,
      },
      update: {
        items: cartItems,
      },
      create: {
        userId: session.user.id,
        items: cartItems,
      },
    });

    return cart;
  } catch (error) {
    console.error("Error saving user cart:", error);
    return null;
  }
};

export const submitOrderAction = async (data: SubmitOrderData) => {
  try {
    const t = await getTranslations("CheckoutForm");
    const validatedData = submitOrderSchema.parse(data);

    const total = validatedData.items.reduce(
      (acc, item) => acc + item.price * item.quantity,
      0,
    );

    const orderReference = new ShortUniqueId().formattedUUID(
      "Kur-$t0$s2-$r4",
      new Date(),
    );

    const order = await prisma.order.create({
      data: { ...validatedData, total, id: orderReference },
    });

    const customerName = `${validatedData.firstName} ${validatedData.lastName}`;
    const emailItems = validatedData.items.map((item) => ({
      name: item.name,
      size: item.size.euSize,
      quantity: item.quantity,
      price: item.price,
    }));

    await sendEmail({
      to: validatedData.email,
      subject: `Confirmação do Pedido - ${orderReference}`,
      html: orderConfirmationEmailTemplate({
        orderReference,
        customerName,
        items: emailItems,
        total,
        paymentMethod: t(`paymentMethods.${validatedData.paymentMethod}`),
        storeName: validatedData.storeName,
        storeAddress: validatedData.storeAddress,
      }),
    });

    await sendEmail({
      to: "<EMAIL>",
      subject: `Novo Pedido - ${orderReference}`,
      html: orderNotificationEmailTemplate({
        orderReference,
        customerName,
        customerEmail: validatedData.email,
        customerPhone: validatedData.phone,
        items: emailItems,
        total,
        paymentMethod: t(`paymentMethods.${validatedData.paymentMethod}`),
        storeName: validatedData.storeName,
        storeAddress: validatedData.storeAddress,
      }),
    });

    // // Clear user's cart after successful order
    // const session = await auth.api.getSession({ headers: await headers() });
    // if (session?.user.id) {
    //   await prisma.cart.deleteMany({
    //     where: {
    //       userId: session.user.id,
    //     },
    //   });
    // }

    return { success: true, data: orderReference };
  } catch (error) {
    console.error("Error submitting order:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: "Os dados fornecidos não são válidos.",
      };
    }

    return {
      success: false,
      data: "Erro interno do servidor. Tente novamente.",
    };
  }
};
