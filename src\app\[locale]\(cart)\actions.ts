"use server";

import { auth } from "@/lib/auth/server";
import prisma from "@/lib/prisma";
import { headers } from "next/headers";
import { CartItem } from "@/app/[locale]/(cart)/cartStore";

export const getUserCart = async () => {
  try {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user.id) return null;

    const cart = await prisma.cart.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    return cart;
  } catch (error) {
    console.error('Error fetching user cart:', error);
    return null;
  }
};

export const saveCart = async (cartItems: CartItem[]) => {
  try {
    const session = await auth.api.getSession({ headers: await headers() });
    if (!session?.user.id) return null;

    const cart = await prisma.cart.upsert({
        where: {
            userId: session.user.id,
        },
        update: {
            items: cartItems,
        },
        create: {
            userId: session.user.id,
            items: cartItems,
        },
    });

    return cart;
  } catch (error) {
    console.error('Error saving user cart:', error);
    return null;
  }
};