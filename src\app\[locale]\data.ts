import { SizeInfo } from "@/components/SizeSelector";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/assets/imgs/ducarmo-logo.svg";
import RosePalharesLogo from "@/assets/imgs/rose-palhares-logo.svg";
import VisarLogo from "@/assets/imgs/visar-logo.svg";
import type { ElementType } from "react";

export type ShopInfoType = {
  logo: ElementType;
  name: string;
  address: string;
  phone: string;
  logoClassName?: string;
};

export const availableSizes: SizeInfo[] = [
  { euSize: "35", ukSize: "3.5", reference: "women", stock: 2 },
  { euSize: "36", ukSize: "4.5", reference: "women", stock: 1 },
  { euSize: "37", ukSize: "5.5", reference: "women", stock: 15 },
  { euSize: "38", ukSize: "6", reference: "women", stock: 12 },
  { euSize: "39", ukSize: "7", reference: "women", stock: 8 },
  { euSize: "40", ukSize: "6.5", reference: "men", stock: 4 },
  { euSize: "41", ukSize: "7.5", reference: "men", stock: 20 },
  { euSize: "42", ukSize: "8", reference: "men", stock: 18 },
  { euSize: "43", ukSize: "8.5", reference: "men", stock: 0 },
  { euSize: "44", ukSize: "9.5", reference: "men", stock: 1 },
  { euSize: "45", ukSize: "10.5", reference: "men", stock: 0 },
];

export const getShopsData = (translation: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (key: string, ...args: any[]): string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  <T = string>(key: string, ...args: any[]): T;
}): ShopInfoType[] => [
  {
    logo: DucarmoLogo,
    name: translation("stores.store1.name"),
    address: translation("stores.store1.address"),
    phone: translation("stores.store1.phone"),
    logoClassName: "max-h-10",
  },
  {
    logo: RosePalharesLogo,
    name: translation("stores.store2.name"),
    address: translation("stores.store2.address"),
    phone: translation("stores.store2.phone"),
  },
  {
    logo: VisarLogo,
    name: translation("stores.store3.name"),
    address: translation("stores.store3.address"),
    phone: translation("stores.store3.phone"),
  },
  {
    logo: VisarLogo,
    name: translation("stores.store4.name"),
    address: translation("stores.store4.address"),
    phone: translation("stores.store4.phone"),
  },
];
