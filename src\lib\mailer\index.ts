import { z } from "zod";
import nodemailer from "nodemailer";

const envSchema = z.object({
  SMTP_HOST: z.string().min(1, "SMTP_HOST is required"),
  SMTP_PORT: z.coerce.number().min(1, "SMTP_PORT is required"),
  SMTP_USER: z.string().min(1, "SMTP_USER is required"),
  SMTP_PASS: z.string().min(1, "SMTP_PASS is required"),
  SMTP_FROM: z.string().optional(),
});

const env = envSchema.parse(process.env);

const transporter = nodemailer.createTransport({
  host: env.SMTP_HOST,
  port: env.SMTP_PORT,
  secure: process.env.NODE_ENV === "production",
  auth: {
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
});

export async function sendEmail({
  to,
  subject,
  html,
}: {
  to: string;
  subject: string;
  html: string;
}) {
  const info = await transporter.sendMail({
    from: env.SMTP_FROM || env.SMTP_USER,
    to,
    subject,
    html,
  });

  if (process.env.NODE_ENV !== "production") {
    const previewUrl = nodemailer.getTestMessageUrl?.(info);
    if (previewUrl) console.log(`Preview URL: ${previewUrl}`);
  }
}

export function otpEmailTemplate({
  otp,
  expiresIn,
}: {
  otp: string;
  expiresIn: number;
}) {
  return `
    <div style="font-family: Arial, sans-serif;">
      <h2>Your Verification Code</h2>
      <p>Your one-time password (OTP) is:</p>
      <div style="font-size: 2em; font-weight: bold; margin: 16px 0;">${otp}</div>
      <p>This code will expire in ${Math.floor(expiresIn / 60)} minutes.</p>
      <p>If you did not request this, please ignore this email.</p>
    </div>
  `;
}

export function orderConfirmationEmailTemplate({
  orderReference,
  customerName,
  items,
  total,
  paymentMethod,
  paymentDetails,
  storeName,
  storeAddress,
}: {
  orderReference: string;
  customerName: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  paymentMethod: string;
  paymentDetails?: Array<{ label: string; value?: string }>;
  storeName: string;
  storeAddress: string;
}) {
  const itemsHtml = items
    .map(
      (item) => `
        <tr>
          <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.name}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee;">Tamanho ${item.size}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${(item.price * item.quantity).toLocaleString("pt-AO")} Kz</td>
        </tr>
      `,
    )
    .join("");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Confirmação do Pedido</h2>
      <p>Olá ${customerName},</p>
      <p>Obrigado pelo seu pedido! Aqui estão os detalhes:</p>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="margin-top: 0;">Referência do Pedido: <strong>${orderReference}</strong></h3>

        <h4>Itens do Pedido:</h4>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background: #eee;">
              <th style="padding: 10px; text-align: left;">Produto</th>
              <th style="padding: 10px; text-align: left;">Tamanho</th>
              <th style="padding: 10px; text-align: center;">Quantidade</th>
              <th style="padding: 10px; text-align: right;">Preço</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
        </table>

        <div style="margin-top: 20px; text-align: right;">
          <strong style="font-size: 18px;">Total: ${total.toLocaleString("pt-AO")} Kz</strong>
        </div>

        <h4>Método de Pagamento:</h4>
        <p><strong>${paymentMethod}</strong></p>
        ${
          paymentDetails && paymentDetails.length > 0
            ? `
          <div style="margin-top: 15px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 5px;">
            ${paymentDetails
              .map(
                (detail) => `
              <div style="margin-bottom: 10px;">
                ${
                  detail.value
                    ? `
                  <strong>${detail.label}:</strong> ${detail.value}
                `
                    : `
                  <p style="margin: 5px 0; color: #666; font-style: italic;">${detail.label}</p>
                `
                }
              </div>
            `,
              )
              .join("")}
          </div>
        `
            : ""
        }

        <h4>Loja Selecionada:</h4>
        <p><strong>${storeName}</strong><br>${storeAddress}</p>
      </div>

      <p>Entraremos em contacto consigo em breve para confirmar o seu pedido.</p>
      <p>Obrigado por escolher a Kurguen!</p>

      <hr style="margin: 30px 0;">
      <p style="color: #666; font-size: 12px;">
        Este é um email automático, por favor não responda. Para questões, contacte-nos atravé<NAME_EMAIL>
      </p>
    </div>
  `;
}

export function orderNotificationEmailTemplate({
  orderReference,
  customerName,
  customerEmail,
  customerPhone,
  items,
  total,
  paymentMethod,
  paymentDetails,
  storeName,
  storeAddress,
}: {
  orderReference: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  items: Array<{
    name: string;
    size: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  paymentMethod: string;
  paymentDetails?: Array<{ label: string; value?: string }>;
  storeName: string;
  storeAddress: string;
}) {
  const itemsHtml = items
    .map(
      (item) => `
        <tr>
          <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.name}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee;">Tamanho ${item.size}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${(item.price * item.quantity).toLocaleString("pt-AO")} Kz</td>
        </tr>
      `,
    )
    .join("");

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333;">Novo Pedido Recebido</h2>

      <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3 style="margin-top: 0;">Referência do Pedido: <strong>${orderReference}</strong></h3>

        <h4>Informações do Cliente:</h4>
        <p><strong>Nome:</strong> ${customerName}</p>
        <p><strong>Email:</strong> ${customerEmail}</p>
        <p><strong>Telefone:</strong> ${customerPhone}</p>

        <h4>Itens do Pedido:</h4>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background: #eee;">
              <th style="padding: 10px; text-align: left;">Produto</th>
              <th style="padding: 10px; text-align: left;">Tamanho</th>
              <th style="padding: 10px; text-align: center;">Quantidade</th>
              <th style="padding: 10px; text-align: right;">Preço</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
        </table>

        <div style="margin-top: 20px; text-align: right;">
          <strong style="font-size: 18px;">Total: ${total.toLocaleString("pt-AO")} Kz</strong>
        </div>

        <h4>Método de Pagamento:</h4>
        <p><strong>${paymentMethod}</strong></p>
        ${
          paymentDetails && paymentDetails.length > 0
            ? `
          <div style="margin-top: 15px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 5px;">
            ${paymentDetails
              .map(
                (detail) => `
              <div style="margin-bottom: 10px;">
                ${
                  detail.value
                    ? `
                  <strong>${detail.label}:</strong> ${detail.value}
                `
                    : `
                  <p style="margin: 5px 0; color: #666; font-style: italic;">${detail.label}</p>
                `
                }
              </div>
            `,
              )
              .join("")}
          </div>
        `
            : ""
        }

        <h4>Loja Selecionada:</h4>
        <p><strong>${storeName}</strong><br>${storeAddress}</p>
      </div>

      <p>Por favor, processe este pedido e entre em contacto com o cliente.</p>
    </div>
  `;
}
