"use client";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useCartStore } from "@/app/[locale]/(cart)/cartStore";
import { Button, buttonVariants } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { cn } from "@/lib/utils";
import Text, { textVariants } from "@/components/Text";
import { useState, useMemo, ReactNode, useLayoutEffect } from "react";
import { Separator } from "@/components/ui/separator";
import ChooseShopDropdown from "@/components/ChooseShopDropdown";
import CartListItems from "./CartListItems";
import CartCheckout from "./CartCheckout";
import CartCheckoutForm from "./CartCheckoutForm";
import { Link } from "@/i18n/navigation";

type Props = {
  className?: string;
  children?: ReactNode;
  initFn?: () => void;
};

export const CartDialog = ({ className, children, initFn }: Props) => {
  const t = useTranslations("Cart");
  const { items, removeItem, selectedShop } = useCartStore();
  const [open, setOpen] = useState(false);

  const [showCheckoutForm, setShowCheckoutForm] = useState(false);

  useLayoutEffect(() => {
    initFn?.();
  }, [initFn]);

  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );
  const total = subtotal;
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <button
            className={cn(
              textVariants({ size: "sm" }),
              "hocus:text-accent flex cursor-pointer items-center gap-1",
              className,
            )}
            aria-label={t("openCart")}
          >
            <div className="relative">
              <ShoppingCart className="size-4 sm:size-5" strokeWidth={1.5} />
              {items.length > 0 && (
                <span className="bg-accent text-secondary font-sans-2 absolute -top-2 -right-2 flex h-4 w-4 items-center justify-center rounded-full text-[10px] font-semibold">
                  {items.length}
                </span>
              )}
            </div>
          </button>
        )}
      </DialogTrigger>
      <DialogContent
        hideCloseButton
        className="flex max-w-4xl flex-col gap-4 transition-all duration-300 ease-out sm:max-w-3xl md:max-w-4xl lg:max-w-5xl"
        onCloseAutoFocus={(event) => {
          event.preventDefault();
          document.body.style.pointerEvents = "";
        }}
      >
        <DialogHeader className="flex flex-col gap-4">
          <div className="flex items-center justify-between gap-2 max-sm:flex-col">
            <DialogTitle className="w-full text-2xl font-normal">
              {t("shoppingBag")}
            </DialogTitle>
            <Button variant="link" size="sm" onClick={() => setOpen(false)}>
              {t("continueShopping")}
            </Button>
          </div>
          <div className="flex flex-col items-center gap-5 sm:flex-row">
            <Text as="p" size="sm">
              {t("chooseStore")}
            </Text>
            <ChooseShopDropdown />
          </div>
        </DialogHeader>

        <Separator />

        {items.length === 0 ? (
          <div className="text-muted-foreground py-10 text-center">
            {t("empty")}
          </div>
        ) : (
          <section className="flex max-h-[60dvh] flex-col gap-8 overflow-y-auto p-1">
            <article
              className={cn(
                "mdx:grid-cols-[2fr_1fr] grid grid-cols-1 items-start gap-8",
              )}
            >
              <CartListItems items={items} removeItem={removeItem} />
              <CartCheckout subtotal={subtotal} total={total}>
                <Link
                  href="#checkout"
                  className={cn(
                    buttonVariants({ variant: "default" }),
                    "w-full",
                    (items.length === 0 || showCheckoutForm) &&
                      "bg-muted pointer-events-none opacity-80",
                  )}
                  onClick={() => items.length > 0 && setShowCheckoutForm(true)}
                >
                  {t("checkout")}
                </Link>
              </CartCheckout>
            </article>

            <div
              className={cn(
                "grid grid-rows-[0fr] transition-[grid-template-rows] duration-300 ease-out",
                showCheckoutForm && "grid-rows-[1fr]",
              )}
            >
              <div className="flex flex-col gap-8 overflow-hidden">
                <Separator />
                <CartCheckoutForm selectedShop={selectedShop} />
              </div>
            </div>
          </section>
        )}
      </DialogContent>
    </Dialog>
  );
};
