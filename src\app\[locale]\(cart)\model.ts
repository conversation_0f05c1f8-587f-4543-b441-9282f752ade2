import { z } from "zod";
import { phoneNumberSchema, profileSchema } from "../(auth)/model";
import { PaymentMethod } from "@generated/prisma";

export const paymentMethods = PaymentMethod;

export const checkoutFormSchema = profileSchema
  .pick({
    firstName: true,
    lastName: true,
  })
  .extend({
    phone: phoneNumberSchema,
    email: z.string().email({ message: "Email inválido." }),
    paymentMethod: z.nativeEnum(paymentMethods, {
      message: "Selecione um método de pagamento.",
    }),
  });

export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

export const submitOrderSchema = checkoutFormSchema.extend({
  storeName: z.string().min(1, { message: "Nome da loja é obrigatório." }),
  storeAddress: z
    .string()
    .min(1, { message: "Endereço da loja é obrigatório." }),
  items: z.array(
    z.object({
      name: z.string(),
      price: z.number(),
      size: z.object({
        euSize: z.string(),
        reference: z.enum(["men", "women"]),
      }),
      quantity: z.number(),
    }),
  ),
});

export type SubmitOrderData = z.infer<typeof submitOrderSchema>;
