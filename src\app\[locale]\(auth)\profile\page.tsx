import { auth } from "@/lib/auth/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { ProfileFormType } from "@/app/[locale]/(auth)/model";
import ProfileForm from "@/app/[locale]/(auth)/ProfileForm";
import AuthCard from "@/app/[locale]/(auth)/AuthCard";
import { linksObj } from "@/app/links";

const ProfilePage = async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user.id) {
    redirect(linksObj.signOn.href);
  }

  const userData: ProfileFormType = {
    firstName: session.user.name?.split(" ")[0] ?? "",
    lastName: session.user.name?.split(" ")[1] ?? "",
    phoneNumber: session.user.phoneNumber ?? "",
  };

  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <AuthCard mode="profile">
        <ProfileForm userData={userData} />
      </AuthCard>
    </main>
  );
};

export default ProfilePage;
