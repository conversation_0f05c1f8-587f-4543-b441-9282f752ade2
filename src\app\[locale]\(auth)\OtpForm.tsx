"use client";

import { useState, useTransition } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { authClient } from "@/lib/auth/client";
import { linksObj } from "@/app/links";
import { useRouter } from "@/i18n/navigation";
import type { SignOnFormType } from "./model";

interface OtpFormProps {
  formDataForOtp: SignOnFormType | null;
  onBack: () => void;
}

const OtpForm = ({ formDataForOtp, onBack }: OtpFormProps) => {
  const t = useTranslations("Auth");
  const [otp, setOtp] = useState("");
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleOtpSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formDataForOtp) {
      toast.error(t('formDataMissing'));
      return;
    }
    startTransition(async () => {
      const { data, error } = await authClient.signIn.emailOtp({
        email: formDataForOtp.email,
        otp,
      });
      if (error || !data) {
        console.error("Sign in with OTP error:", error);
        toast.error(t('otpVerificationFailed'));
        return;
      }
      toast.success(t('authenticationSuccessful'));
      if (!data.user.name) {
        router.push(linksObj.profile.href);
        return;
      }
      router.push(linksObj.home.href);
    });
  };

  return (
    <form onSubmit={handleOtpSubmit}>
      <div className="grid gap-6">
        <div className="text-center">
          <h3 className="text-lg font-medium">{t("verificationTitle")}</h3>
          <p className="text-muted-foreground mt-1 text-sm">
            {t("verificationDesc")}
          </p>
        </div>
        <div className="flex justify-center">
          <InputOTP
            value={otp}
            onChange={setOtp}
            maxLength={6}
            pattern={REGEXP_ONLY_DIGITS}
          >
            <InputOTPGroup>
              <InputOTPSlot index={0} />
              <InputOTPSlot index={1} />
              <InputOTPSlot index={2} />
            </InputOTPGroup>
            <InputOTPGroup>
              <InputOTPSlot index={3} />
              <InputOTPSlot index={4} />
              <InputOTPSlot index={5} />
            </InputOTPGroup>
          </InputOTP>
        </div>
        <Button type="submit" size="sm" className="flex-2" disabled={isPending}>
          {t("verify")}
          {isPending && <Loader2 className="size-4 animate-spin p-0.5" />}
        </Button>
        <Button
          size="sm"
          type="button"
          variant="link"
          className="mx-auto w-fit"
          onClick={onBack}
        >
          {t("back")}
        </Button>
      </div>
    </form>
  );
};

export default OtpForm;
